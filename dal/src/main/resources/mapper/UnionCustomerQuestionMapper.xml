<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinfei.vocmng.dal.mapper.UnionCustomerQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinfei.vocmng.dal.po.UnionCustomerQuestion">
        <id column="id" property="id" />
        <result column="user_name_cipher" property="userNameCipher" />
        <result column="mobile_cipher" property="mobileCipher" />
        <result column="mobile_protyle" property="mobileProtyle" />
        <result column="mobile_protyle_cipher" property="mobileProtyleCipher" />
        <result column="id_card_number_cipher" property="idCardNumberCipher" />
        <result column="id_card_protyle" property="idCardProtyle" />
        <result column="id_card_protyle_cipher" property="idCardProtyleCipher" />
        <result column="user_source" property="userSource" />
        <result column="product_type" property="productType" />
        <result column="question_content" property="questionContent" />
        <result column="question_result" property="questionResult" />
        <result column="create_person_cipher" property="createPersonCipher" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="order_number" property="orderNumber" />
        <result column="app_type" property="appType" />
        <result column="last_person_cipher" property="lastPersonCipher" />
        <result column="last_time" property="lastTime" />
        <result column="question_type" property="questionType" />
        <result column="user_category" property="userCategory" />
        <result column="is_black" property="isBlack" />
        <result column="is_work" property="isWork" />
        <result column="fund_source" property="fundSource" />
        <result column="utm_source" property="utmSource" />
        <result column="inner_app" property="innerApp" />
        <result column="app" property="app" />
        <result column="user_id" property="userId" />
        <result column="in_mobile_protyle" property="inMobileProtyle" />
        <result column="in_mobile_protyle_cipher" property="inMobileProtyleCipher" />
        <result column="in_mobile_cis" property="inMobileCis" />
        <result column="out_mobile_protyle" property="outMobileProtyle" />
        <result column="out_mobile_protyle_cipher" property="outMobileProtyleCipher" />
        <result column="out_mobile_cis" property="outMobileCis" />
        <result column="is_dark_industry" property="isDarkIndustry" />
        <result column="dark_industry_reason" property="darkIndustryReason" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name_cipher, mobile_cipher, mobile_protyle, mobile_protyle_cipher, id_card_number_cipher, id_card_protyle, id_card_protyle_cipher, user_source, product_type, question_content, question_result, create_person_cipher, create_time, update_time, order_number, app_type, last_person_cipher, last_time, question_type, user_category, is_black, is_work, fund_source, utm_source, inner_app, app, user_id, in_mobile_protyle, in_mobile_protyle_cipher, in_mobile_cis, out_mobile_protyle, out_mobile_protyle_cipher, out_mobile_cis, is_dark_industry, dark_industry_reason
    </sql>

</mapper>
