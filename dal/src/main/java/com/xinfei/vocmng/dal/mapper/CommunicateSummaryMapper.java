package com.xinfei.vocmng.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会话小结表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Mapper
public interface CommunicateSummaryMapper extends BaseMapper<CommunicateSummary> {

    List<CommunicateSummary> getFiveSummaries(@Param("telephone") String telephone, @Param("summaryId") Long summaryId);

    Integer getSummariesSevenDay(@Param("userNo") Long userNo, @Param("time") LocalDate time);

    List<CommunicateSummary> getEagleEyeData(@Param("sourceList") List<Integer> sourceList,
                                             @Param("createUserIdentifyList") List<String> createUserIdentifyList,
                                             @Param("statusList") List<Integer> statusList,
                                             @Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate);


}
