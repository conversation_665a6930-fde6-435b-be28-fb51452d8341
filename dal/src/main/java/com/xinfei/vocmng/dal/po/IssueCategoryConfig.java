package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 问题分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("issue_category_config")
public class IssueCategoryConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 分类名称 */
    @TableField("name")
    private String name;

    /** 上级问题分类ID */
    @TableField("parent_id")
    private Long parentId;

    /** 问题分类级别：1一级 2二级3三级 */
    @TableField("level")
    private Integer level;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 是否隐藏：0不隐藏 1隐藏 */
    @TableField("is_hide")
    private Integer isHide;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
