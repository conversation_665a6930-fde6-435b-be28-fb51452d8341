package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("operate_log")
public class OperateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 请求URL */
    @TableField("url")
    private String url;

    /** 请求参数 */
    @TableField("request_param")
    private String requestParam;

    /** 响应 */
    @TableField("response")
    private String response;

    /** 操作类型 */
    @TableField("type")
    private Integer type;

    /** 操作描述 */
    @TableField("description")
    private String description;


    /** 操作人身份标识 */
    @TableField("user_identify")
    private String userIdentify;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
