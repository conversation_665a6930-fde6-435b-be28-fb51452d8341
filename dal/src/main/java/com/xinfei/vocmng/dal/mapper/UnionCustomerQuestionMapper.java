package com.xinfei.vocmng.dal.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinfei.vocmng.dal.po.SummaryEntity;
import com.xinfei.vocmng.dal.po.UnionCustomerQuestion;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10 02:50:49
 */
//@Mapper
@DS("union")
public interface UnionCustomerQuestionMapper extends BaseMapper<UnionCustomerQuestion> {

    @Select("SELECT a.id, a.user_name_cipher, a.mobile_cipher, a.mobile_protyle, a.mobile_protyle_cipher, a.id_card_number_cipher, a.id_card_protyle, a.id_card_protyle_cipher, a.user_source, a.product_type, a.question_content, a.question_result, a.create_person_cipher, a.create_time, a.update_time, a.order_number, a.app_type, a.last_person_cipher, a.last_time, a.question_type, a.user_category, a.is_black, a.is_work, a.fund_source, a.utm_source, a.inner_app, a.app, a.user_id, a.in_mobile_protyle, a.in_mobile_protyle_cipher, a.in_mobile_cis, a.out_mobile_protyle, a.out_mobile_protyle_cipher, a.out_mobile_cis, a.is_dark_industry, a.dark_industry_reason" +
            " FROM union_customer_question a " +
            "where a.id > #{idStart} and a.id <= #{idEnd}")
    List<SummaryEntity> queryQuestionByRange(@Param("idStart") Long idStart, @Param("idEnd") Long idEnd);

    @Select("SELECT id FROM `union_customer_question` order by id desc limit 1")
    Long queryTopId();

    @Select("SELECT count(1) FROM `union_customer_question` where id >= #{start} and id <=#{end}")
    Long queryCount(@Param("start") Long start, @Param("end") Long end);

}
