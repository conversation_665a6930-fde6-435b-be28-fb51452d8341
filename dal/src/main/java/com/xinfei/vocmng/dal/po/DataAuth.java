/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据权限表
 * <AUTHOR>
 * @version $ DataAuth, v 0.1 2023/12/20 17:29 wancheng.qu Exp $
 */

@Data
@TableName("data_auth")
public class DataAuth {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 数据信息 */
    @TableField("data")
    private String data;

    /** 数据类型，1:信息掩码 */
    @TableField("data_type")
    private Integer dataType;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

}