package com.xinfei.vocmng.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummaryRemarkCopy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 会话小结备注表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Mapper
public interface CommunicateSummaryRemarkCopyMapper extends BaseMapper<CommunicateSummaryRemarkCopy> {

    @Select("SELECT a.id, a.communicate_summary_id, a.remark, a.create_user_identify, a.is_deleted, a.created_time, a.updated_time,  a.follow_up_record " +
            " FROM communicate_summary_remark_copy a " +
            "where a.communicate_summary_id > #{idStart} and a.communicate_summary_id <= #{idEnd}")
    List<CommunicateSummaryRemarkCopy> queryRemarkByRange(@Param("idStart") Long idStart, @Param("idEnd") Long idEnd);

}
