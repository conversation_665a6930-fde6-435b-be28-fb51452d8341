package com.xinfei.vocmng.biz.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.dto.IVROrderInfoDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.request.GetOrderDetailRequest;
import com.xinfei.vocmng.biz.rr.request.GetOrderListRequest;
import com.xinfei.vocmng.biz.rr.response.Paging;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class OrderBillServiceImpTest {

    @Resource
    private LoanServiceImp orderBillServiceImp;

    @Test
    public void queryOrderList() {
        GetOrderListRequest request = new GetOrderListRequest();
        request.setUserNos(new ArrayList<String>(){{
            add("1639203089095980781");
            add("1639203089095980780");
        }});
        request.setLoanStatus(new ArrayList<String>(){{
            add("RP");}});
        request.setIsCustomerDetail(Boolean.FALSE);
        request.setOrderStatuses(new ArrayList<String>() {{
            add("03");
        }});
        Paging<OrderDto> orderDtoPaging = orderBillServiceImp.queryOrderList(request);
        System.out.println(orderDtoPaging);
    }

    @Test
    public void queryIvrOrderList() {
        IVROrderInfoDto ivrOrderInfoDto = orderBillServiceImp.queryIVROrderList(new ArrayList<Long>() {{
            add(111111111283756L);
            add(111111111283754L);
        }});
        System.out.println(JSONObject.toJSONString(ivrOrderInfoDto));
    }

    @Test
    public void queryOrderDetail() {
        GetOrderDetailRequest request = new GetOrderDetailRequest();
        request.setOrderNo("2024041100002600000016646515");
        request.setOrderType("MAIN");
        OrderDto orderDto = orderBillServiceImp.queryOrderDetail(request);
        System.out.println(orderDto);
    }
}