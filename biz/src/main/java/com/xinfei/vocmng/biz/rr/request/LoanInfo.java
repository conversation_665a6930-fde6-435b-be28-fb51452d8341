/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class LoanInfo {

    @ApiModelProperty("还款类型：1：还多期，2：提前结清")
    @NotNull(message = "还款类型不能为空")
    private Integer planType;

    @ApiModelProperty("借据号")
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty(value = "账单状态:0-未到期,1-逾期,2-本期结清")
    private String rpyFlag;

    @ApiModelProperty("总期数")
    private String totalTerm;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;
}