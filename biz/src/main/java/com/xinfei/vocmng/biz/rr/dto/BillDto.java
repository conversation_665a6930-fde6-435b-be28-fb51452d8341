/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class BillDto {

    @ApiModelProperty(value = "总期数")
    private Integer totalTerms;

    @ApiModelProperty(value = "利率")
    private BigDecimal feeRate;

    @ApiModelProperty("账单对应订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("提前结清费")
    private BigDecimal fee4;

    @ApiModelProperty("结清挡板金额")
    private BigDecimal settleAmtLimit;

    @ApiModelProperty("红线减免金额")
    private BigDecimal redDeductAmt;

    @ApiModelProperty("是否支持提前还款")
    private Boolean canRepay;

    @ApiModelProperty(value = "PayPlanDomain:期数")
    private Integer term;

    @ApiModelProperty(value = "PayPlanDomain:账单状态:0-未到期,1-逾期,2-本期结清")
    private String rpyFlag;

    @ApiModelProperty(value = "PayPlanDomain:账单号（还款计划编号）")
    private String planNo;

    @ApiModelProperty(value = "PayPlanDomain:本金")
    private BigDecimal prinAmt;

    @ApiModelProperty(value = "PayPlanDomain:应还利息（利息）")
    private BigDecimal intAmt;

    @ApiModelProperty(value = "PayPlanDomain:已还利息（实还利息）")
    private BigDecimal actIntAmt;

    @ApiModelProperty(value = "PayPlanDomain:罚息")
    private BigDecimal ointAmt;

    @ApiModelProperty(value = "PayPlanDomain:担保逾期（贷后逾期管理费）")
    private BigDecimal fee3Amt;

    @ApiModelProperty(value = "PayPlanDomain:到期应还（总额），没有把减免排除")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "PayPlanDomain:计划担保费（贷后管理费）已还")
    private BigDecimal actFee1Amt;

    @ApiModelProperty(value = "PayPlanDomain:应还（贷后管理费）")
    private BigDecimal fee1Amt;

    @ApiModelProperty(value = "PayPlanDomain:已还计划咨询费（平台服务费）已还")
    private BigDecimal actFee2Amt;

    @ApiModelProperty(value = "PayPlanDomain:应还计划咨询费（平台服务费）应还")
    private BigDecimal fee2Amt;

    @ApiModelProperty(value = "PayPlanDomain:账单日（到期日）")
    private LocalDate dateDue;

    @ApiModelProperty(value = "PayPlanDomain:容时期限（宽限期）")
    private LocalDate dateGrace;

    @ApiModelProperty(value = "PayPlanDomain:结清时间（结清日期）")
    private LocalDateTime dateSettle;

    @ApiModelProperty(value = "PayPlanDomain:应还总额（到期实际应还总额）")
    private BigDecimal remainTotalAmtWithDeduct;

    @ApiModelProperty(value = "PayPlanDomain:已还总额")
    private BigDecimal actTotalAmt;

    @ApiModelProperty(value = "PayPlanDomain:应还罚息")
    private BigDecimal fee3AndOintAmt;

    @ApiModelProperty(value = "PayPlanDomain:已还罚息")
    private BigDecimal actFee3AndOintAmt;

    @ApiModelProperty(value = "PayPlanDomain:创建日期")
    private LocalDateTime dateCreated;


}