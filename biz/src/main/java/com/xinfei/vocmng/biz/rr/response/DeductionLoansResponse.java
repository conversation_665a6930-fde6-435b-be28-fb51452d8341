/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ PlanDetailRequest, v 0.1 2024-03-28 22:15 junjie.yan Exp $
 */
@Data
public class DeductionLoansResponse {

    @ApiModelProperty("借据号")
    String loanNo;

    @ApiModelProperty("订单号")
    String orderNo;

    @ApiModelProperty("账单号")
    String planNo;

    @ApiModelProperty("实际还款金额")
    BigDecimal transAmt;

    @ApiModelProperty("可核销金额")
    BigDecimal canReductionAmt;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date dateCreated;

    @ApiModelProperty("最早创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date earliestCreateTime;
}