/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.DictApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.enums.AmtTypeEnum;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ DictController, v 0.1 2024/8/14 09:56 you.zhang Exp $
 */
@Slf4j
@RestController
@LoginRequired
public class DictController implements DictApi {
    @Override
    public ApiResponse<Map<String, String>> queryAdjustType() {
        return ApiResponse.success(Arrays.stream(AmtTypeEnum.values()).collect(Collectors.toMap(AmtTypeEnum::getCode, AmtTypeEnum::getMsg)));
    }
}
