/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import com.xinfei.vocmng.biz.rr.request.FeeAmountDtoResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentResponse, v 0.1 2024-03-25 13:37 junjie.yan Exp $
 */

@Data
public class CalculateFee {

    /**
     * 共有信息
     */
    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("试算本金")
    private BigDecimal calPrinAmt;

    @ApiModelProperty("试算利息")
    private BigDecimal calIntAmt;

    @ApiModelProperty("试算担保费")
    private BigDecimal calGuaranteeFee;

    @ApiModelProperty("担保费")
    private BigDecimal transFee1;

    @ApiModelProperty("反担保费")
    private BigDecimal transFee2;

    @ApiModelProperty("试算逾期费")
    private BigDecimal calLateFee;

    @ApiModelProperty("罚息")
    private BigDecimal transOint;
    @ApiModelProperty("贷后逾期管理费")
    private BigDecimal transFee3;
    @ApiModelProperty("催费")
    private BigDecimal transFee6;

    @ApiModelProperty("试算应还总额")
    private BigDecimal calTotalAmt;

    @ApiModelProperty("红线减免")
    private BigDecimal redDeductAmt;

    @ApiModelProperty("挡板命中场景  TRIAL-客服测算 TRIAL_PROCESS-还款+试算")
    private String settleBaffleScene;

    @ApiModelProperty("试算本金红线")
    private BigDecimal redCalPrinAmt;

    @ApiModelProperty("试算利息红线")
    private BigDecimal redCalIntAmt;

    @ApiModelProperty("试算担保费红线")
    private BigDecimal redCalGuaranteeFee;

    @ApiModelProperty("担保费红线")
    private BigDecimal redTransFee1;

    @ApiModelProperty("反担保费红线")
    private BigDecimal redTransFee2;

    @ApiModelProperty("试算逾期费红线")
    private BigDecimal redCalLateFee;

    @ApiModelProperty("提前还款手续费红线")
    private BigDecimal redTransFee4;

    @ApiModelProperty("罚息红线")
    private BigDecimal redTransOint;
    @ApiModelProperty("贷后逾期管理费红线")
    private BigDecimal redTransFee3;
    @ApiModelProperty("催费红线")
    private BigDecimal redTransFee6;

    /**
     * 还当期
     */
    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty(value = "是否支持提前还款")
    private Boolean isAdvanceCurr;


    @ApiModelProperty("期数计划明细")
    private List<PlanCalculateFee> planCalculateFees;

    /**
     * 结清
     */
    @ApiModelProperty("提前结清费(账单列表transDetail的transFee4之和),TRIAL_PROCESS时会减去红线减免")
    private BigDecimal advanceSettlementFee;

    @ApiModelProperty("提前结清费,不包含红线减免")
    private BigDecimal realAdvSettFee;

    @ApiModelProperty("借据状态. RP-正常, OD-逾期, FP-结清")
    private String loanStatus;

    @ApiModelProperty("方案失效时间")
    private LocalDateTime endTime;

    @ApiModelProperty("可抵扣金额下限")
    private BigDecimal amountLower;

    @ApiModelProperty("可抵扣金额上限")
    private BigDecimal amountUpper;

    @ApiModelProperty("可减免+抵扣金额")
    private FeeAmountDtoResp canDeductAmtDetail;
}