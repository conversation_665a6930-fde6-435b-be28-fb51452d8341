package com.xinfei.vocmng.biz.remote;

import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.dal.po.CapitalMail;
import com.xinfei.vocmng.dal.po.SendMail;
import com.xinfei.vocmng.itl.rr.ContractStatusDetail;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.ProtocolDto;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import com.xinfei.vocmng.itl.rr.dto.ContractDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import com.xinfei.vocmng.biz.rr.dto.DocumentRecordDto;
import com.xinfei.vocmng.itl.rr.dto.LoanInfoDto;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 合同相关服务
 *
 * <AUTHOR>
 * @version $ ContractRemoteService, v 0.1 2023/12/23 16:32 qu.lu Exp $
 */
public interface ContractRemoteService {
    /**
     * 根据营收订单号查询合同信息
     *
     * @param request
     * @return
     */
    ApiResponse<List<ContractDetailDto>> queryCashSubContractList(QueryContractListRequest request);

    ApiResponse<LoanInfoDto> queryJqzmInfo(LoanInfoRequest request);

    ApiResponse<List<String>> querySpecialList();

    ApiResponse<List<ContractStatusDetail>> queryObtainStatus(List<String> orderIds);

    ApiResponse<Boolean> sendMail(SettlementCertificateReq req);

    ApiResponse<List<String>> downFile(SettlementCertificateReq req);

    ApiResponse<String> applyNow(SettlementCertificateReq req);

    ApiResponse<PageResultResponse<DocumentRecordDto>> queryApplyList(DocumentRecordReq record);

    ApiResponse<LookFileResp> lookFile(DocumentRecordReq record);

    ApiResponse<Boolean> uploadDocumentRecord(MultipartFile[] file, String mail, Integer type, Long id);

    ApiResponse<List<CapitalMail>> queryCapitalList();

    ApiResponse<Boolean> updateCapital(CapitalMail req);

    ApiResponse<Boolean> insertCapital(CapitalMail req);

    ApiResponse<List<SendMail>> querySendList(SendMail req);

    ApiResponse<Boolean> insertSend(SendMail req);

    ApiResponse<Boolean> updateSend(SendMail req);

    ApiResponse<String> sendFile(DocumentRecordReq record);

    ApiResponse<List<ContractDataDto>> queryContractList(ContractReq req);

    ApiResponse<List<ProtocolDto>> getProtocolsByType(ContractReq req);

    ApiResponse<String> resignContract(ResignContractReq req);

    ApiResponse<List<ContractVO>> queryContractCoreList(ContractCoreReq req);

    ImmutablePair<String, List<String>> sendSpecialMail(Map<String, List<LoanInfoRequest>> specialList, String name, Long userNo);
}
