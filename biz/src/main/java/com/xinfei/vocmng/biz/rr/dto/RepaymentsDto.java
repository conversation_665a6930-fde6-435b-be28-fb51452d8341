/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class RepaymentsDto {

    @ApiModelProperty("还款单号")
    private String repaymentNo;

    @ApiModelProperty("还款发起时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("账单号")
    private List<String> planNos;

    @ApiModelProperty("借据单号(订单号)")
    private String loanNo;

    @ApiModelProperty("交易总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("扣款成功金额明细")
    private RepayFeeDetailDto successTransDetail;

    @ApiModelProperty("扣款成功金额:元")
    private BigDecimal amount;

    @ApiModelProperty("还款方式")
    private String payType;

    @ApiModelProperty("还款方式Code")
    private String payTypeCode;

    @ApiModelProperty("还款卡号")
    private String cardNumber;

    @ApiModelProperty("银行代码")
    private String bankCode;

    @ApiModelProperty("还款渠道")
    private String channelCode;

    @ApiModelProperty("所有扣款单号")
    private List<String> deductionNos;

    @ApiModelProperty("还款结果")
    private String payStatus;

    @ApiModelProperty("结果说明")
    private String failedReason;

    @ApiModelProperty("操作人")
    private String createdBy;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("还款单减免总金额")
    private BigDecimal deductTotalAmt;

    @ApiModelProperty("销账流水信息列表")
    private List<RepayFlowDetailDto> liveFlowDetailList;

    @ApiModelProperty("扣款单列表")
    private List<DeductionInfoDto> deductionInfos;

    @ApiModelProperty("是否可退款撤销")
    private boolean canCancel;
}