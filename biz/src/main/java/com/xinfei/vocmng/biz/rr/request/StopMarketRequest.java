/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StopMarketRequest, v 0.1 2024/8/16 15:21 wancheng.qu Exp $
 */
@Data
public class StopMarketRequest extends PageRequestDto implements Serializable {

    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "状态,status=1生效 （禁止营销)" + "status=0就是失效（恢复营销）")
    private String status;
    @ApiModelProperty(value = "更新开始时间")
    private String beginTime;
    @ApiModelProperty(value = "更新结束时间")
    private String endTime;
    @ApiModelProperty(value = "加黑原因")
    private String disableReason;
    @ApiModelProperty(value = "app列表,新增用")
    private List<String> appList;
    @ApiModelProperty(value = "app，修改用")
    private String app;
}