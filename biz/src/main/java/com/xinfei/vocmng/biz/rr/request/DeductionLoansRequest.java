/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @version $ PlanDetailRequest, v 0.1 2024-03-28 22:15 junjie.yan Exp $
 */
@Data
public class DeductionLoansRequest {

    @ApiModelProperty("custNo")
    @NotBlank
    private String custNo;

    @ApiModelProperty("还款类型（1：还当期，2：结清）")
    @NotNull
    private Integer repayType;
}