package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/6/21 11:15
 * LoadRightCardRefundRequest
 */
@Data
public class VipCardRefundLog {
    @ApiModelProperty("会员卡id：黑卡，会员卡，飞享会员卡订单id cardId")
    @NotNull(message = "vipCardId不为空")
    private Long vipCardId;

    @ApiModelProperty("卡类型:1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡，4：飞跃会员")
    @NotNull(message = "cardType不为空")
    private Integer cardType;

    @ApiModelProperty("支付订单号")
    private String orderNo;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("过期时间")
    private String endTime;

    @ApiModelProperty("会员类型 1：月卡，2:连续包月，3:季卡，4:连续包季")
    private Integer vipTerm;

    @ApiModelProperty("订单操作类型：1:首次购买，2:手动续期，3-自动续期，4-手动关闭，5-自动关闭，6-提前续费")
    private Integer orderBuyType;

    @ApiModelProperty("支付金额:元")
    private BigDecimal payAmount;

    @ApiModelProperty("APP用户ID")
    private String userId;
}
