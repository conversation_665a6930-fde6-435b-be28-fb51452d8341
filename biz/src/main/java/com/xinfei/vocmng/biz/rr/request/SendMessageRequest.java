/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ SendCouponRequest, v 0.1 2023/10/28 20:59 junjie.yan Exp $
 */
@Data
public class SendMessageRequest {

    @ApiModelProperty(value = "userNo")
    @NotBlank
    private String userNo;

    @ApiModelProperty(value = "短信模板id")
    @NotBlank
    private String templateId;

    private Map<String, String> data;

    @ApiModelProperty(value = "mobile")
    private String mobile;

    @ApiModelProperty(value = "app")
    @NotBlank
    private String app;
}