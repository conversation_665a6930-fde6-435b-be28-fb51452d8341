package com.xinfei.vocmng.biz.service;

import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.IVROrderInfoDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface LoanService {

    Paging<OrderDto> queryOrderList(GetOrderListRequest request);

    OrderDto queryOrderDetail(GetOrderDetailRequest request);

    Boolean orderCancel(String orderNo);

    String queryByOutOrderNumber(GetOrderListRequest request);

    String queryByChannelOrderNumber(GetOrderListRequest request);

    List<LoanPlanDto> queryBillList(GetBillListRequest request);

    CanRepayResponse queryCanRepay(CanRepayRequest request);

    Paging<RepaymentsDto> queryRepayments(GetRepaymentsRequest request);

    Paging<DiversionOrderDto> queryApiOrderList(ApiOrderRequest apiOrderRequest);

    List<ProductDetailInfo> queryProductList();

    LegalAgencyDetail queryAgencyDetail(AgencyDetailRequest agencyDetailRequest);

    void orderListDownload(HttpServletResponse httpServletResponse,GetBillListRequest request);

    IVROrderInfoDto queryIVROrderList(String customNo, String mobile);
}
