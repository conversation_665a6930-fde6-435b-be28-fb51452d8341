/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ SseProducer, v 0.1 2024/3/7 17:25 wancheng.qu Exp $
 */
@Component
@Slf4j
public class SseProducer {

    @Autowired
    private MqTemplate mqTemplate;

    public void sendSseMsg(Map<String, Object> data) {
        String msg = JsonUtil.toJson(data);
        log.info("sendSseMsg data:{}",msg);
        try {
             mqTemplate.syncSend("tp_vocmng_sse",msg);
        } catch (Exception e) {
            log.error("sendSseMsg error,msg:{},error:{}",msg, e.getMessage());
        }
    }

}