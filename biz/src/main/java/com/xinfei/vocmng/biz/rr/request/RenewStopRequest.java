/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $ RenewStopRequest, v 0.1 2024-07-04 16:05 junjie.yan Exp $
 */
@Data
public class RenewStopRequest {

    @NotBlank(message = "userNo不能为空")
    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("会员卡类型：1老会员卡  2新会员卡 3飞享会员 4飞跃会员")
    @NotNull(message = "会员卡类型不能为空")
    private Integer type;

    @ApiModelProperty("飞享/飞跃会员状态")
    @NotBlank(message = "飞享/飞跃会员状态不能为空")
    private String vipStatus;

}
